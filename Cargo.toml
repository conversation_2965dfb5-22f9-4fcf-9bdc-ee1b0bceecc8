[package]
name = "rustwin"
edition = "2021"
rust-version = "1.84"
publish = false

[dependencies]
bytemuck = { version = "1.23", features = [ "derive" ] }
env_logger = "0.11.8"
nalgebra = "0.33.2"
pollster = "0.4"
rand = "0.9.1"
wgpu = "25.0.0"
winit = { version = "0.30.8" }

[[example]]
name = "triangle"
path = "examples/triangle.rs"

[[example]]
name = "triangle_select"
path = "examples/triangle_select.rs"
