
use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::Vertex,
    render::{TriangleList, triangle_render_pipeline},
    window::WindowState,
    window_app::{SceneState, run_application},
};


// Coordinates for a triangle.
const MY_VERTICES: &[Vertex] = &[
    Vertex { position: [0.0, 0.5, 0.0],   color: [1.0, 0.0, 0.0] },
    Vertex { position: [-0.5, -0.5, 0.0], color: [0.0, 1.0, 0.0] },
    Vertex { position: [0.5, -0.5, 0.0],  color: [0.0, 0.0, 1.0] },
];

const MY_INDICES: &[u16] = &[0, 1, 2];


struct MyTriangleRenderer {
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    triangles: TriangleList
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );
        let triangles = TriangleList::new(&wstate.device, MY_VERTICES, MY_INDICES);

        return Self {
            camera_state,
            render_pipeline,
            triangles,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, _queue: &mut wgpu::Queue) {}

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.triangles.render(render_pass);
    }

    fn mouse_moved(&mut self, _x: f32, _y: f32) {}
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
