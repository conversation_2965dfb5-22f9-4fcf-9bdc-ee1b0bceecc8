
use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::GpuVertex,
    render::{TriangleList, triangle_render_pipeline},
    window::WindowState,
    window_app::{SceneState, run_application},
};

use rand::Rng;
use nalgebra::{Point2};

// Triangle size (smaller than the original)
const TRIANGLE_SIZE: f32 = 0.15;


// Generate a triangle at a specific position with different colors for each vertex
fn create_triangle_at_position(center_x: f32, center_y: f32, colors: &[[f32; 3]; 3]) -> [GpuVertex; 3] {
    return [
        GpuVertex { position: [center_x, center_y + TRIANGLE_SIZE, 0.0], color: colors[0] },
        GpuVertex { position: [center_x - TRIANGLE_SIZE, center_y - TRIANGLE_SIZE, 0.0], color: colors[1] },
        GpuVertex { position: [center_x + TRIANGLE_SIZE, center_y - TRIANGLE_SIZE, 0.0], color: colors[2] },
    ];
}

// Check if two circles (representing triangle bounds) overlap
fn circles_overlap(x1: f32, y1: f32, x2: f32, y2: f32, radius: f32) -> bool {
    let dx = x1 - x2;
    let dy = y1 - y2;
    let distance_squared = dx * dx + dy * dy;
    let min_distance = radius * 2.0;
    return distance_squared < min_distance * min_distance;
}

// Generate non-overlapping positions for triangles
fn generate_non_overlapping_positions(count: usize) -> Vec<(f32, f32)> {
    let mut rng = rand::rng();
    let mut positions = Vec::new();
    let margin = TRIANGLE_SIZE * 1.5; // Safety margin around triangles
    let bounds = 0.8; // Keep triangles within visible bounds

    for _ in 0..count {
        let mut attempts = 0;
        let max_attempts = 10;

        while attempts < max_attempts {
            let x = rng.random_range(-bounds..bounds);
            let y = rng.random_range(-bounds..bounds);

            // Check if this position overlaps with any existing position
            let overlaps = positions.iter().any(|(px, py)| {
                circles_overlap(x, y, *px, *py, margin)
            });

            if !overlaps {
                positions.push((x, y));
                break;
            }
            attempts += 1;
        }
    }
    return positions;
}

// Generate random colors for vertices (3 colors per triangle)
fn generate_random_vertex_colors(triangle_count: usize) -> Vec<[f32; 3]> {
    let mut rng = rand::rng();
    return (0..(triangle_count * 3))
        .map(|_| [rng.random(), rng.random(), rng.random()])
        .collect();
}

// Generate all vertices and indices for multiple triangles
fn generate_triangle_vertices(colors: &[[f32; 3]]) -> Vec<GpuVertex> {
    let triangle_count = colors.len() / 3;
    let positions = generate_non_overlapping_positions(triangle_count);
    let mut vertices = Vec::new();

    for (i, (x, y)) in positions.iter().enumerate() {
        let vcolors = [colors[i*3], colors[i*3 + 1], colors[i*3 + 2]];
        let triangle_vertices = create_triangle_at_position(*x, *y, &vcolors);
        vertices.extend_from_slice(&triangle_vertices);
        println!("Created triangle {:?}.", triangle_vertices);
    }

    println!("Created {:?} vertices.", vertices.len());
    return vertices;
}

// Test if a point is inside a triangle using barycentric coordinates
fn point_in_triangle(point: Point2<f32>, v0: Point2<f32>, v1: Point2<f32>, v2: Point2<f32>) -> bool {
    // Compute vectors
    let v0v1 = v1 - v0;
    let v0v2 = v2 - v0;
    let v0p = point - v0;

    // Compute dot products
    let dot00 = v0v2.dot(&v0v2);
    let dot01 = v0v2.dot(&v0v1);
    let dot02 = v0v2.dot(&v0p);
    let dot11 = v0v1.dot(&v0v1);
    let dot12 = v0v1.dot(&v0p);

    // Compute barycentric coordinates
    let inv_denom = 1.0 / (dot00 * dot11 - dot01 * dot01);
    let u = (dot11 * dot02 - dot01 * dot12) * inv_denom;
    let v = (dot00 * dot12 - dot01 * dot02) * inv_denom;

    // println!("in_triangle u={:.4} v={:.4}", u, v);

    // Check if point is in triangle
    (u >= 0.0) && (v >= 0.0) && (u + v <= 1.0)
}

// Find which triangle contains the given world point
fn find_triangle_at_point(world_point: Point2<f32>, vertices: &[GpuVertex]) -> Option<usize> {
    let num_triangles = vertices.len() / 3;
    for vi in 0..num_triangles {
        let i = vi*3;
        let v0 = &vertices[i];
        let v1 = &vertices[i+1];
        let v2 = &vertices[i+2];
        let p0 = Point2::new(v0.position[0], v0.position[1]);
        let p1 = Point2::new(v1.position[0], v1.position[1]);
        let p2 = Point2::new(v2.position[0], v2.position[1]);
        if point_in_triangle(world_point, p0, p1, p2) {
            return Some(vi);
        }
    }
    return None;
}


struct MyTriangleRenderer {
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    triangles: TriangleList,

    // Store triangle data for selection and highlighting.
    vertex_colors: Vec<[f32; 3]>,
    selected_triangle: Option<usize>,
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );

        // Generate the triangle data.
        let vertex_colors = generate_random_vertex_colors(10);
        let vertices = generate_triangle_vertices(&vertex_colors[..]);
        let indices: Vec<u16> = (0..(vertices.len() as u16)).collect();
        let triangles = TriangleList::new(&wstate.device, &vertices, &indices);

        return Self {
            camera_state,
            render_pipeline,
            triangles,
            vertex_colors,
            selected_triangle: None,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        self.triangles.update_buffer(queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.triangles.render(render_pass);
    }

    fn mouse_moved(&mut self, x: f32, y: f32) {
        // Convert screen coordinates to world coordinates using stored window dimensions
        let world_point =
            self.camera_state.camera.screen_to_world(x as f32, y as f32).xy();
        // println!("mouse_moved {:?}", world_point);

        // Find which triangle is under the cursor
        let new_selected = find_triangle_at_point(world_point, &self.triangles.cpu_vertex_buffer);

        // If selection changed, update colors
        if new_selected != self.selected_triangle {
            // Restore original color of the currently selected triangle.
            if let Some(idx) = self.selected_triangle {
                for i in 0..3 {
                    self.triangles.cpu_vertex_buffer[idx*3 + i].color = self.vertex_colors[idx*3 + i];
                }
            };
            // Set newly selected triangle to white.
            if let Some(idx) = new_selected {
                for i in 0..3 {
                    self.triangles.cpu_vertex_buffer[idx*3 + i].color = [1.0, 1.0, 1.0];
                }
            }

            self.selected_triangle = new_selected;
            println!(
                "Selected triangle: {:?} at world coords ({:.3}, {:.3})",
                new_selected, world_point.x, world_point.y
            );
        }
    }
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
