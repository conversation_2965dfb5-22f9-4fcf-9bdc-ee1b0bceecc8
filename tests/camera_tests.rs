use rustwin::graphics::camera::Camera;
use nalgebra::Matrix4;

#[test]
fn test_view_projection_matrix_inverse() {
    // Test with default camera
    let camera = Camera::default();

    let view_proj = camera.build_view_projection_matrix();
    let inv_view_proj = camera.build_inverse_view_projection_matrix();

    // Multiply the matrices - should give identity matrix
    let product = view_proj * inv_view_proj;
    let identity = Matrix4::<f32>::identity();

    // Check that the product is approximately equal to identity
    // We use a small epsilon for floating point comparison
    let epsilon: f32 = 1e-6;
    for i in 0..4 {
        for j in 0..4 {
            let diff: f32 = (product[(i, j)] - identity[(i, j)]).abs();
            assert!(diff < epsilon,
                "Matrix multiplication result at ({}, {}) = {}, expected {}, diff = {}",
                i, j, product[(i, j)], identity[(i, j)], diff);
        }
    }
}

#[test]
fn test_view_projection_matrix_inverse_with_different_aspect_ratios() {
    // Test with different aspect ratios
    let aspect_ratios = [0.5, 1.0, 1.5, 2.0];

    for aspect_ratio in aspect_ratios {
        let mut camera = Camera::default();
        camera.aspect_ratio = aspect_ratio;

        let view_proj = camera.build_view_projection_matrix();
        let inv_view_proj = camera.build_inverse_view_projection_matrix();

        // Multiply the matrices - should give identity matrix
        let product = view_proj * inv_view_proj;
        let identity = Matrix4::<f32>::identity();

        // Check that the product is approximately equal to identity
        let epsilon: f32 = 1e-6;
        for i in 0..4 {
            for j in 0..4 {
                let diff: f32 = (product[(i, j)] - identity[(i, j)]).abs();
                assert!(diff < epsilon,
                    "Matrix multiplication result at ({}, {}) = {}, expected {}, diff = {} (aspect_ratio = {})",
                    i, j, product[(i, j)], identity[(i, j)], diff, aspect_ratio);
            }
        }
    }
}

#[test]
fn test_view_projection_matrix_inverse_with_different_scales() {
    // Test with different scales
    let scales = [0.5, 1.0, 2.0, 5.0];

    for scale in scales {
        let mut camera = Camera::default();
        camera.scale = scale;

        let view_proj = camera.build_view_projection_matrix();
        let inv_view_proj = camera.build_inverse_view_projection_matrix();

        // Multiply the matrices - should give identity matrix
        let product = view_proj * inv_view_proj;
        let identity = Matrix4::<f32>::identity();

        // Check that the product is approximately equal to identity
        let epsilon: f32 = 1e-6;
        for i in 0..4 {
            for j in 0..4 {
                let diff: f32 = (product[(i, j)] - identity[(i, j)]).abs();
                assert!(diff < epsilon,
                    "Matrix multiplication result at ({}, {}) = {}, expected {}, diff = {} (scale = {})",
                    i, j, product[(i, j)], identity[(i, j)], diff, scale);
            }
        }
    }
}
