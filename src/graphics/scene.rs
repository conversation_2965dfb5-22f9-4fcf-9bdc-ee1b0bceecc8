// Defines the scene graph for a 2D or 3D world.

use crate::graphics::{
    gpu_buffer::{TransformationMatrix, Vertex},
    render::TriangleList
};

use nalgebra::{
    Point3, Point4, Vector3, Matrix4, Scale3, Similarity3
};

use wgpu::util::DeviceExt;  // For device.create_buffer_init.


#[derive(<PERSON><PERSON>, <PERSON>lone)]
pub struct CpuVertex {
    pub position: Point3<f32>,
    pub color: Point3<f32>
}


pub struct Body {
    pub id: i32,
    pub position: Point3<f32>,
    pub rotation: f32,
    pub view_transform: Matrix4<f32>,
    pub cpu_vertices: Vec<CpuVertex>,
}

impl Body {
    pub fn new(position: Point3<f32>, cpu_vertices: &[CpuVertex]) -> Body {
        return Body {
            id: 0,
            position,
            rotation: 0.0,
            view_transform: get_view_transform(position, 0.0),
            cpu_vertices: cpu_vertices.to_vec()
        };
    }

    pub fn get_view_transform(position: &Point3<f32>, rotation: f32) -> Matrix4<f32> {
        let rot = Rotation3::from_axis_angle(&Vector3::z_axis(), rotation);
        
    }
}



pub struct Scene {
    bodies: Vec<Body>,
    triangles: TriangleList
}

impl Scene {
    pub fn new(bodies: &[Bodies]) -> Scene {
    }



    pub fn update_buffer(&mut self, queue: &mut wgpu::Queue) {
        self.triangles.update_buffer(queue);
    }

    pub fn render(&self, render_pass: &mut wgpu::RenderPass) {
        // Rendering code.
        self.triangles.render(render_pass);
    }
}