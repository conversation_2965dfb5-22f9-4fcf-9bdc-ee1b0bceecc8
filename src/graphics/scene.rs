// Defines the scene graph for a 2D or 3D world.

use crate::graphics::{
    gpu_buffer::CpuVertex,
    render::TriangleList,
};

use nalgebra::{
    Isometry3, Matrix4, Point3, Translation3, UnitQuaternion, Vector3
};


pub const MAX_VERTICES: usize = 1024;

pub struct Body {
    pub id: usize,
    pub offset: usize,
    pub position: Point3<f32>,
    pub rotation: f32,
    pub cpu_vertices: Vec<CpuVertex>,
    view_matrix: Matrix4<f32>,
    transformed_vertices: Vec<CpuVertex>,
}


impl Body {
    pub fn new(position: Point3<f32>, cpu_vertices: &[CpuVertex]) -> Body {
        return Body {
            id: 0,
            offset: 0,
            position,
            rotation: 0.0,
            cpu_vertices: cpu_vertices.to_vec(),
            view_matrix: Matrix4::identity(),
            transformed_vertices: vec!(CpuVertex::default(); cpu_vertices.len())
        };
    }

    // Recalculate the view matrix and transformed vertices.
    pub fn update_view_matrix(&mut self) {
        self.view_matrix = get_view_matrix(&self.position, self.rotation);

        let numv = self.cpu_vertices.len();
        for i in 0..numv {
            let hpos = self.cpu_vertices[i].position.to_homogeneous();
            let npos = self.view_matrix * hpos;
            self.transformed_vertices[i] = CpuVertex {
                position: npos.xyz().into(),
                // position: self.cpu_vertices[i].position + self.position.coords,
                color: self.cpu_vertices[i].color
            };
        }
    }

    // Write the transformed vector coordinates to the given TriangleList.
    pub fn write_to_triangle_list(&self, tlist: &mut TriangleList) -> usize {
        let offset = self.offset;
        let numv = self.cpu_vertices.len();
        for i in 0..numv {
            tlist.set(offset + i, self.transformed_vertices[i]);
        }
        return numv
    }
}



pub struct Scene {
    pub bodies: Vec<Body>,
    triangles: TriangleList
}

impl Scene {
    pub fn new(device: &wgpu::Device) -> Scene {
        let bodies: Vec<Body> = Vec::new();
        let triangles = TriangleList::new(device, MAX_VERTICES);
        return Scene {
            bodies,
            triangles
        }
    }

    pub fn add(&mut self, body: Body) {
        self.bodies.push(body);
    }

    pub fn update_buffer(&mut self, queue: &mut wgpu::Queue) {
        // Transform and copy the vertices from all bodies to the triangle list.
        let mut offset: usize = 0;
        for i in 0..self.bodies.len() {
            self.bodies[i].id = i;
            self.bodies[i].offset = offset;
            offset += self.bodies[i].write_to_triangle_list(&mut self.triangles);
        }
        self.triangles.num_vertices = offset;
        // Copy triangle data to the GPU.
        self.triangles.update_buffer(queue);
    }

    pub fn render(&self, render_pass: &mut wgpu::RenderPass) {
        self.triangles.render(render_pass);
    }
}


fn get_view_matrix(position: &Point3<f32>, rotation: f32) -> Matrix4<f32> {
    let trans = Translation3::from(position.clone());
    let rot = UnitQuaternion::from_axis_angle(&Vector3::z_axis(), rotation);
    let iso = Isometry3::from_parts(trans, rot);
    return iso.to_homogeneous();
}
