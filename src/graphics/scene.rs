// Defines the scene graph for a 2D or 3D world.

use crate::graphics::{
    geometry::{BoundingBox, point_in_triangle},
    gpu_buffer::CpuVertex,
    render::TriangleList,
};

use nalgebra::{
    Isometry3, Matrix4, Point2, Point3, Translation3, UnitQuaternion, Vector3
};


pub const MAX_VERTICES: usize = 1024;
pub const MAX_INDICES: usize = 1024;


pub struct Body {
    pub id: usize,
    pub v_offset: usize,
    pub i_offset: usize,
    pub position: Point3<f32>,
    pub rotation: f32,
    pub cpu_vertices: Vec<CpuVertex>,
    pub cpu_indices: Vec<u16>,
    view_matrix: Matrix4<f32>,
    transformed_vertices: Vec<CpuVertex>,
    bounding_box: BoundingBox
}


impl Body {
    pub fn new(position: Point3<f32>, cpu_vertices: &[CpuVertex], cpu_indices: &[u16]) -> Body {
        return Body {
            id: 0,
            v_offset: 0,
            i_offset: 0,
            position,
            rotation: 0.0,
            cpu_vertices: cpu_vertices.to_vec(),
            cpu_indices: cpu_indices.to_vec(),
            view_matrix: Matrix4::identity(),
            transformed_vertices: vec!(CpuVertex::default(); cpu_vertices.len()),
            bounding_box: BoundingBox::new()
        };
    }

    // Recalculate the view matrix and transformed vertices.
    pub fn update_view_matrix(&mut self) {
        self.view_matrix = get_view_matrix(&self.position, self.rotation);

        let numv = self.cpu_vertices.len();
        for i in 0..numv {
            let hpos = self.cpu_vertices[i].position.to_homogeneous();
            let npos = self.view_matrix * hpos;
            self.transformed_vertices[i] = CpuVertex {
                position: npos.xyz().into(),
                // position: self.cpu_vertices[i].position + self.position.coords,
                color: self.cpu_vertices[i].color
            };
        }

        self.bounding_box = BoundingBox::from_vertices(self.transformed_vertices.as_slice());
    }

    // Write the transformed vector coordinates to the given TriangleList.
    // Returns pair of (num_vertices, num_indices).
    pub fn write_to_triangle_list(&self, tlist: &mut TriangleList, is_selected: bool) -> (usize, usize) {
        let v_offset = self.v_offset;
        let i_offset = self.i_offset;
        let numv = self.cpu_vertices.len();
        let numi = self.cpu_indices.len();

        for i in 0..numv {
            tlist.set(v_offset + i, self.transformed_vertices[i]);
            // TODO: this is a hack to set the color of the selected triangle.
            if is_selected {
                tlist.cpu_vertex_buffer[v_offset + i].color = [1.0, 1.0, 1.0];
            }
        }
        for j in 0..numi {
            tlist.set_index(i_offset + j, (i_offset as u16) + self.cpu_indices[j])
        }
        return (numv, numi);
    }

    pub fn inside(&self, p: &Point3<f32>) -> bool {
        if !self.bounding_box.inside(p) {
            return false;
        }
        let ntri = self.transformed_vertices.len() / 3;
        for i in 0..ntri {
            let n = i*3;
            let a = self.transformed_vertices[n].position.xy();
            let b = self.transformed_vertices[n+1].position.xy();
            let c = self.transformed_vertices[n+2].position.xy();
            if point_in_triangle(p.xy(), a, b, c) {
                return true;
            }
        }
        return false;
    }
}



pub struct Scene {
    pub bodies: Vec<Body>,
    triangles: TriangleList,
    selected: Option<usize>,
}

impl Scene {
    pub fn new(device: &wgpu::Device) -> Scene {
        let bodies: Vec<Body> = Vec::new();
        let triangles = TriangleList::new(device, MAX_VERTICES, MAX_INDICES);
        return Scene {
            bodies,
            triangles,
            selected: None
        }
    }

    pub fn add(&mut self, body: Body) {
        self.bodies.push(body);
    }

    pub fn select(&mut self, p: Point2<f32>) {
        let p3 = Point3::<f32>::new(p.x, p.y, 0.5);
        self.selected = None;
        for i in 0..self.bodies.len() {
            if self.bodies[i].inside(&p3) {
                self.selected = Some(i)
            }
        }
    }

    pub fn update_buffer(&mut self, queue: &mut wgpu::Queue) {
        // Transform and copy the vertices from all bodies to the triangle list.
        let mut v_offset: usize = 0;
        let mut i_offset: usize = 0;
        for i in 0..self.bodies.len() {
            let is_selected = match self.selected {
                Some(j) => i == j,
                None => false
            };

            self.bodies[i].id = i;
            self.bodies[i].v_offset = v_offset;
            self.bodies[i].i_offset = i_offset;
            let (v_out, i_out) =
                self.bodies[i].write_to_triangle_list(&mut self.triangles, is_selected);
            v_offset += v_out;
            i_offset += i_out;
        }
        self.triangles.num_vertices = v_offset;
        self.triangles.num_indices = i_offset;
        // Copy triangle data to the GPU.
        self.triangles.update_buffer(queue);
    }

    pub fn render(&self, render_pass: &mut wgpu::RenderPass) {
        self.triangles.render(render_pass);
    }
}


fn get_view_matrix(position: &Point3<f32>, rotation: f32) -> Matrix4<f32> {
    let trans = Translation3::from(position.clone());
    let rot = UnitQuaternion::from_axis_angle(&Vector3::z_axis(), rotation);
    let iso = Isometry3::from_parts(trans, rot);
    return iso.to_homogeneous();
}
