
use std::sync::Arc;
use winit::window::Window;


/// Contains a reference to a window, along with state needed to draw to the window.
/// Implements the basic functionality needed to open a window and create a surface.
pub struct WindowState {
    pub window: Arc<Window>,
    pub surface: wgpu::Surface<'static>,
    pub device: wgpu::Device,
    pub queue: wgpu::Queue,

    pub size: winit::dpi::PhysicalSize<u32>,
    pub surface_format: wgpu::TextureFormat,
    pub surface_config: wgpu::SurfaceConfiguration,
}

impl WindowState {
    /// Create a new WindowState for the given window.
    /// Also initializes state for the graphics pipeline.
    pub async fn new(window: Arc<Window>) -> WindowState {
        // The instance is used to give us a surface, and an adapter.
        let instance = wgpu::Instance::new(&wgpu::InstanceDescriptor::default());

        // The surface is used for drawing.
        let surface = instance.create_surface(window.clone()).unwrap();

        // The Adapter is a handle to our the physical GPU card.
        // It is locked to a particular backend, e.g. Vulkan or DirectX.
        // let adapter = instance
        //    .request_adapter(&wgpu::RequestAdapterOptions::default())
        //    .await
        //    .unwrap();
        let adapter = instance.request_adapter(
            &wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::default(),
                compatible_surface: Some(&surface),
                force_fallback_adapter: false,
            },
        ).await.unwrap();

        // The Device is an open connection to the graphics card, and is used to manage resources. 
        // The Queue is used to submit graphics commands.
        let (device, queue) = adapter
            .request_device(&wgpu::DeviceDescriptor::default())
            .await
            .unwrap();

        // --- Configure the Surface ---
        let size = window.inner_size();
        let surface_caps = surface.get_capabilities(&adapter);
        let surface_format = surface_caps.formats[0];

        let surface_config = wgpu::SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format: surface_format,
            width: size.width,
            height: size.height,

            // Request compatibility with the sRGB-format texture view we‘re going to create later.
            view_formats: vec![surface_format.add_srgb_suffix()],
            alpha_mode: wgpu::CompositeAlphaMode::Auto,
            present_mode: wgpu::PresentMode::AutoVsync,
            desired_maximum_frame_latency: 2,
        };
        surface.configure(&device, &surface_config);

        let state = WindowState {
            window,
            surface,
            device,
            queue,
            size,
            surface_format,
            surface_config,
        };
        return state;
    }

    pub fn resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        // Called in response to a window resize event.

        eprintln!("Resizing to {:?}", new_size);
        self.size = new_size;

        // Reconfigure the surface with the new size.
        self.surface_config.width = new_size.width;
        self.surface_config.height = new_size.height;
        self.surface.configure(&self.device, &self.surface_config);
    }

    pub fn render<F>(&mut self, render_fn: F) where F: FnOnce(&mut wgpu::RenderPass) {
        // Called in response to a window redraw event.
        // render_fn does the actual drawing.

        // Get the texture for the window.  
        let surface_texture = self
            .surface
            .get_current_texture()
            .expect("Failed to acquire next swapchain texture.");
        // Get a TextureView, which is used to render to the window in renderpass.
        let surface_texture_view = surface_texture
            .texture
            .create_view(&wgpu::TextureViewDescriptor {
                // Without add_srgb_suffix() the image we will be working with
                // might not be "gamma correct".
                format: Some(self.surface_format.add_srgb_suffix()),
                ..Default::default()
            });

        // Encodes a list of render and/or compute passes into GPU commands.
        let mut encoder = self.device.create_command_encoder(&Default::default());

        // Create the RenderPass which will clear the screen to black.
        let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
            label: Some("Basic Render Pass"),
            color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                view: &surface_texture_view,
                resolve_target: None,
                ops: wgpu::Operations {
                    load: wgpu::LoadOp::Clear(wgpu::Color::BLACK),
                    store: wgpu::StoreOp::Store,
                },
            })],
            depth_stencil_attachment: None,
            timestamp_writes: None,
            occlusion_query_set: None,
        });

        render_fn(&mut render_pass);

        // End the renderpass, thus releasing encoder.
        drop(render_pass);

        // Submits the completed command buffer.
        self.queue.submit([encoder.finish()]);
        // Inform winit that we're about to update the window.
        self.window.pre_present_notify();
        // Display the now-rendered texture in the window.
        surface_texture.present();
    }
}

