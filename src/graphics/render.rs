// Render pipelines.

use crate::graphics::{
    gpu_buffer::{CpuVertex, GpuVertex},
    camera::CameraState,
    window::WindowState,
};

use wgpu::util::DeviceExt;  // For device.create_buffer_init.


/// Creates a RenderPipeline suitable for drawing triangles.
pub fn triangle_render_pipeline(
    window_state: &WindowState,
    camera_state: &CameraState,
) -> wgpu::RenderPipeline {
    let device = &window_state.device;
    let surface_config = &window_state.surface_config;
    let camera_bind_group_layout = &camera_state.bind_group_layout;

    let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
        label: Some("Shader"),
        source: wgpu::ShaderSource::Wgsl(include_str!("shader.wgsl").into()),
    });

    let render_pipeline_layout = device.create_pipeline_layout(
        &wgpu::PipelineLayoutDescriptor {
            label: Some("Simple PipelineLayout"),
            bind_group_layouts: &[
                camera_bind_group_layout,
            ],
            push_constant_ranges: &[],
        }
    );

    let render_pipeline = device.create_render_pipeline(&wgpu::RenderPipelineDescriptor {
        label: Some("Simple RenderPipeline"),
        layout: Some(&render_pipeline_layout),
        vertex: wgpu::VertexState {
            module: &shader,
            entry_point: Some("vs_main"),
            buffers: &[
                GpuVertex::vbuffer_layout()
            ],
            compilation_options: wgpu::PipelineCompilationOptions::default(),
        },
        fragment: Some(wgpu::FragmentState {
            module: &shader,
            entry_point: Some("fs_main"),
            targets: &[Some(wgpu::ColorTargetState {
                format: surface_config.format,
                blend: Some(wgpu::BlendState::REPLACE),
                write_mask: wgpu::ColorWrites::ALL,
            })],
            compilation_options: wgpu::PipelineCompilationOptions::default(),
        }),
        primitive: wgpu::PrimitiveState {
            topology: wgpu::PrimitiveTopology::TriangleList,   // Every 3 vertices = 1 triangle.
            strip_index_format: None,
            front_face: wgpu::FrontFace::Ccw,
            cull_mode: Some(wgpu::Face::Back),
            polygon_mode: wgpu::PolygonMode::Fill,  // anything else requires Features::NON_FILL_POLYGON_MODE
            unclipped_depth: false,                 // true requires Features::DEPTH_CLIP_CONTROL
            conservative: false,                    // true requires Features::CONSERVATIVE_RASTERIZATION
        },
        depth_stencil: None,
        multisample: wgpu::MultisampleState {
            count: 1,
            mask: !0,
            alpha_to_coverage_enabled: false,
        },
        multiview: None,
        cache: None,
    });

    return render_pipeline;
}



/// GPU buffers containing a list of triangles, with associated rendering code.
pub struct TriangleList {
    pub num_vertices: usize,
    pub max_vertices: usize,
    pub cpu_vertex_buffer: Vec<GpuVertex>,
    vertex_buffer: wgpu::Buffer,
    index_buffer: wgpu::Buffer,
}

impl TriangleList {
    pub fn new(device: &wgpu::Device, max_vertices: usize) -> TriangleList {
        // Preallocate arrays with the maximum number of vertices.
        let num_vertices: usize = 0;
        let cpu_vertex_buffer = vec![GpuVertex::default(); max_vertices];
        let indices: Vec<u16> = (0..(max_vertices as u16)).collect();

        // Create a GPU vertex buffer.
        let vertex_buffer = device.create_buffer_init(
            &wgpu::util::BufferInitDescriptor {
                label: Some("Vertex Buffer"),
                contents: bytemuck::cast_slice(cpu_vertex_buffer.as_slice()),
                usage: wgpu::BufferUsages::VERTEX | wgpu::BufferUsages::COPY_DST,
            }
        );
        let index_buffer = device.create_buffer_init(
            &wgpu::util::BufferInitDescriptor {
                label: Some("Index Buffer"),
                contents: bytemuck::cast_slice(indices.as_slice()),
                usage: wgpu::BufferUsages::INDEX | wgpu::BufferUsages::COPY_DST,
            }
        );

        return TriangleList {
            num_vertices,
            max_vertices,
            cpu_vertex_buffer,
            vertex_buffer,
            index_buffer,
        }
    }

    pub fn add(&mut self, cvertex: CpuVertex) {
        self.cpu_vertex_buffer[self.num_vertices] = GpuVertex {
            position: cvertex.position.into(),
            color: cvertex.color.into()
        };
        self.num_vertices += 1;
    }

    pub fn set(&mut self, i: usize, cvertex: CpuVertex) {
        self.cpu_vertex_buffer[i] = GpuVertex {
            position: cvertex.position.into(),
            color: cvertex.color.into()
        };
    }

    pub fn update_buffer(&mut self, queue: &mut wgpu::Queue) {
        let vertices = &self.cpu_vertex_buffer[..];
        queue.write_buffer(&self.vertex_buffer, 0, bytemuck::cast_slice(vertices));
    }

    pub fn render(&self, render_pass: &mut wgpu::RenderPass) {
        // Rendering code.
        render_pass.set_vertex_buffer(0, self.vertex_buffer.slice(..));
        render_pass.set_index_buffer(self.index_buffer.slice(..), wgpu::IndexFormat::Uint16);
        render_pass.draw_indexed(0..self.num_vertices as u32, 0, 0..1);
    }
}