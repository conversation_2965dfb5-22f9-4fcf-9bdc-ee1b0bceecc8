// GPU Buffers.

use nalgebra::{Matrix4, Point3};

/// a vertex, in a format that can be easily manipulated on CPU.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Debug)]
pub struct CpuVertex {
    pub position: Point3<f32>,
    pub color: Point3<f32>
}


/// A vertex, in a format that can be easily sent to the GPU shader.
#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Debug, bytemuck::Pod, bytemuck::Zeroable)]
pub struct GpuVertex {
    pub position: [f32; 3],
    pub color: [f32; 3],
}

impl GpuVertex {
    pub fn vbuffer_layout() -> wgpu::VertexBufferLayout<'static> {
        // Returns a layout so that Vertex arrays can be used as vertex buffers.
        // This layout must match the definition of VertexInput in the shader.
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<GpuVertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &[
                wgpu::VertexAttribute {   // location(0)
                    offset: 0,
                    shader_location: 0,
                    format: wgpu::VertexFormat::Float32x3,
                },
                wgpu::VertexAttribute {   // location(1)
                    offset: std::mem::size_of::<[f32; 3]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: wgpu::VertexFormat::Float32x3,
                }
            ]
        }
    }
}


/// A model, view, or projection matrix, in a format that can be transferred to the GPU shader.
#[repr(C)]
#[derive(Debug, Copy, Clone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct GpuTransformationMatrix {
    // We can't use nalgebra with bytemuck directly, so we'll have
    // to convert the Matrix4 into a 4x4 f32 array
    pub matrix4: [[f32; 4]; 4]
}

impl GpuTransformationMatrix {
    /// Initializes the projection matrix to the identity matrix.
    pub fn new() -> Self {
        return Self {
            matrix4: Matrix4::identity().into()
        };
    }
}
