// =============
// Vertex shader

struct CameraUniform {
    view_proj: mat4x4<f32>,
};
@group(0) @binding(0)
var<uniform> camera: CameraUniform;

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) color: vec3<f32>,
};

struct VertexOutput {
    // @builtin(position) is in clip coordinates when output by the vertex shader.
    // @builtin(position) is in framebuffer coordinates when input into the fragment shader.    
    @builtin(position) clip_position: vec4<f32>,
    @location(0) color: vec3<f32>,
};

@vertex
fn vs_main(v_in: VertexInput) -> VertexOutput {
    var v_out: VertexOutput;
    v_out.color = v_in.color;
    v_out.clip_position = camera.view_proj * vec4<f32>(v_in.position, 1.0);
    return v_out;
}


// ===============
// Fragment shader

@fragment
fn fs_main(f_in: VertexOutput) -> @location(0) vec4<f32> {
    return vec4<f32>(f_in.color, 1.0);
}


