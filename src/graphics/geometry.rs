
use std::cmp::min;
use crate::graphics::gpu_buffer::CpuVertex;

use nalgebra::{Point2, Point3};


pub struct BoundingBox {
    pub minp: Point3<f32>,
    pub maxp: Point3<f32>
}

impl BoundingBox {
    pub fn from_vertices(vs: &[CpuVertex]) -> BoundingBox {
        let mut minp = vs[0].position;
        let mut maxp = vs[0].position;
        for v in vs {
            minp = minp.simd_min(v);
            maxp = maxp.simd_max(v);
        }
        return BoundingBox {
            minp,
            maxp
        }
    }
}





// Test if a point is inside a triangle using barycentric coordinates
fn point_in_triangle(point: Point2<f32>, v0: Point2<f32>, v1: Point2<f32>, v2: Point2<f32>) -> bool {
    // Compute vectors
    let ab = v1 - v0;
    let ac = v2 - v0;
    let ap = point - v0;

    // Compute dot products
    let acc = ac.dot(&ac);
    let abb = ab.dot(&ab);
    let acb = ac.dot(&ab);
    let abp = ab.dot(&ap);
    let acp = ac.dot(&ap);

    // Compute barycentric coordinates
    let inv_denom = 1.0 / (acc * abb - acb * acb);
    let u = (abb * acp - acb * abp) * inv_denom;
    let v = (acc * abp - acb * acp) * inv_denom;

    // Check if point is in triangle
    (u >= 0.0) && (v >= 0.0) && (u + v <= 1.0)
}