// Defines a camera in a 2D or 3D world.

use crate::graphics::{
    gpu_buffer::TransformationMatrix,
};

use nalgebra::{
    Point3, Point4, Vector3, Matrix4, Scale3, Similarity3
};

use wgpu::util::DeviceExt;  // For device.create_buffer_init.


// Converts clip coordinates z-axis so that it ranges from 0 to 1, instead of -1 to 1.
pub const OPENGL_TO_WGPU_MATRIX: Matrix4<f32> = Matrix4::new(
    1.0, 0.0, 0.0, 0.0,
    0.0, 1.0, 0.0, 0.0,
    0.0, 0.0, 0.5, 0.5,
    0.0, 0.0, 0.0, 1.0,
);

// Inverse of OPENGL_TO_WGPU_MATRIX
pub const OPENGL_TO_WGPU_MATRIX_INV: Matrix4<f32> = Matrix4::new(
    1.0, 0.0, 0.0, 0.0,
    0.0, 1.0, 0.0, 0.0,
    0.0, 0.0, 2.0, -1.0,
    0.0, 0.0, 0.0, 1.0,
);


/// A camera in a 2D or 3D scene.
/// Builds a projection matrix, for use in a render pipeline.
pub struct Camera {
    pub eye_position: Point3<f32>,   // Position of the camera.
    pub direction: Vector3<f32>,     // Direction that the camera is facing.
    pub up: Vector3<f32>,            // Vector pointing in the up direction.
    pub scale: f32,                  // Zoom in or out.
    pub aspect_ratio: f32,           // width/height of window

    pub view_projection_matrix: Matrix4<f32>,
    pub inverse_view_projection_matrix: Matrix4<f32>,
}

impl Camera {
    pub fn default() -> Self {
        let eye_position = Point3::new(0.0, 0.0, 0.0);
        let direction = Vector3::new(0.0, 0.0, -1.0);
        let up = Vector3::new(0.0, 1.0, 0.0);
        let mut camera = Self {
            eye_position,
            direction,
            up,
            scale: 1.0,
            aspect_ratio: 1.0,

            view_projection_matrix: Matrix4::<f32>::identity(),
            inverse_view_projection_matrix: Matrix4::<f32>::identity(),
        };
        camera.update_projection_matrices();
        return camera;
    }

    /// Returns the view projection matrix.
    /// This matrix converts from world-space coordinates to clip coordinates.
    /// Clip coordinates range from -1 to 1 for the x and y axes, and 0 to 1 for the z axis.
    pub fn build_view_projection_matrix(&self) -> Matrix4<f32> {
      let target = self.eye_position + self.direction;
      let view_proj = Similarity3::look_at_rh(
          &self.eye_position, &target, &self.up, self.scale
      ).to_homogeneous();

      // Normalized device coords map from -1 to 1 on the x-axis and y-axis,
      // regardless of screen aspect ratio, which will stretch the image.
      // If width > height, then shrink X to undo the stretch, otherwise shrink Y.
      let scale_ar = Scale3::new(
        (1.0 / self.aspect_ratio).min(1.0),
        self.aspect_ratio.min(1.0),
        1.0_f32
      ).to_homogeneous();

      return OPENGL_TO_WGPU_MATRIX * scale_ar * view_proj;
    }

    /// Returns the inverse of the view projection matrix.
    /// This can be used to convert from clip-space coordinates to world-space coordinates.
    /// Clip coordinates range from -1 to 1 for the x and y axes, and 0 to 1 for the z axis.
    pub fn build_inverse_view_projection_matrix(&self) -> Matrix4<f32> {
        // Build the inverse by inverting each transformation in reverse order:
        // Original: OPENGL_TO_WGPU_MATRIX * scale_ar * view_proj
        // Inverse: view_proj^-1 * scale_ar^-1 * OPENGL_TO_WGPU_MATRIX^-1
        let target = self.eye_position + self.direction;
        let view_proj = Similarity3::look_at_rh(
            &self.eye_position, &target, &self.up, self.scale
        );
        let inv_view_proj = view_proj.inverse().to_homogeneous();

        // Then, get the inverse of the aspect ratio scaling
        let inv_scale_ar = Scale3::new(
            self.aspect_ratio.max(1.0),
            (1.0 / self.aspect_ratio).max(1.0),
            1.0_f32
        ).to_homogeneous();

        return inv_view_proj * inv_scale_ar * OPENGL_TO_WGPU_MATRIX_INV;
    }

    /// Recompute the forward and inverse projection matrices after a change to the camera.
    pub fn update_projection_matrices(&mut self) {
        self.view_projection_matrix = self.build_view_projection_matrix();
        self.inverse_view_projection_matrix = self.build_inverse_view_projection_matrix();
    }

    /// Convert 3D world coordinates to 3D clip coordinates using the projection matrix.
    pub fn clip_to_world(&self, p: Point3<f32>) -> Point3<f32> {
        let ph: Point4<f32> = p.to_homogeneous().into();
        let world_coords: Point4<f32> = self.view_projection_matrix * ph;
        return world_coords.xyz();
    }

    /// Convert 2D screen-space (clip) coordinates to world coordinates using the inverse projection matrix.
    pub fn screen_to_world(&self, x: f32, y: f32) -> Point3<f32> {
        // Create a 4D vector in clip space (z=0.5 for 2D plane, w=1)
        let clip_coords = Point4::new(x as f32, y as f32, 0.5, 1.0);
        let world_coords = self.inverse_view_projection_matrix * clip_coords;
        return world_coords.xyz();
    }
}


/// The camera projection matrix, for use with shaders.
#[repr(C)]
#[derive(Debug, Copy, Clone, bytemuck::Pod, bytemuck::Zeroable)]
pub struct CameraUniform(TransformationMatrix);

impl CameraUniform {
    /// Initializes the projection matrix to the identity matrix.
    pub fn new() -> Self {
        return Self(TransformationMatrix::new());
    }

    /// Initialize the projection matrix from a Camera.
    pub fn from_camera(camera: &Camera) -> Self {
        return Self(TransformationMatrix {
            matrix4: camera.view_projection_matrix.into()
        });
    }

    /// Updates the value of the projection matrix to the one defined by the Camera.
    pub fn update_from_camera(&mut self, camera: &Camera) {
        self.0.matrix4 = camera.view_projection_matrix.into();
    }
}


/// Holds a camera, and the state necessary to use that camera in shader code.
/// Computes a projection matrix from the camera, and stores that matrix in a
/// bind group on GPU.
pub struct CameraState {
    pub camera: Camera,
    pub camera_uniform: CameraUniform,
    pub buffer: wgpu::Buffer,
    pub bind_group_layout: wgpu::BindGroupLayout,
    pub bind_group: wgpu::BindGroup,
}

impl CameraState {
    /// Create a camera and projection matrix along with associated GPU data.
    pub fn new(device: &wgpu::Device) -> Self {
        let camera = Camera::default();
        let camera_uniform = CameraUniform::from_camera(&camera);

        let buffer = device.create_buffer_init(
            &wgpu::util::BufferInitDescriptor {
                label: Some("Camera Buffer"),
                contents: bytemuck::cast_slice(&[camera_uniform]),
                usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
            }
        );

        let bind_group_layout = device.create_bind_group_layout(
            &wgpu::BindGroupLayoutDescriptor {
                entries: &[
                    wgpu::BindGroupLayoutEntry {
                        binding: 0,
                        visibility: wgpu::ShaderStages::VERTEX,
                        ty: wgpu::BindingType::Buffer {
                            ty: wgpu::BufferBindingType::Uniform,
                            has_dynamic_offset: false,
                            min_binding_size: None,
                        },
                        count: None,
                    }
                ],
                label: Some("camera_bind_group_layout"),
            }
        );

        let bind_group = device.create_bind_group(
            &wgpu::BindGroupDescriptor {
                layout: &bind_group_layout,
                entries: &[
                    wgpu::BindGroupEntry {
                        binding: 0,
                        resource: buffer.as_entire_binding(),
                    }
                ],
                label: Some("camera_bind_group"),
            }
        );

        return CameraState {
            camera,
            camera_uniform,
            buffer,
            bind_group_layout,
            bind_group
        }
    }

    /// Set the aspect ratio of the Camera, e.g. when the window is resized.
    pub fn set_aspect_ratio(&mut self, width: u32, height: u32) {
        let ratio = (width as f32) / (height as f32);
        self.camera.aspect_ratio = ratio;
        self.camera.update_projection_matrices();
    }

    /// Update the projection matrix stored on GPU with current Camera state.
    pub fn update_camera_buffer(&mut self, queue: &mut wgpu::Queue) {
        self.camera_uniform.update_from_camera(&self.camera);
        queue.write_buffer(&self.buffer, 0, bytemuck::cast_slice(&[self.camera_uniform]));
    }
}
