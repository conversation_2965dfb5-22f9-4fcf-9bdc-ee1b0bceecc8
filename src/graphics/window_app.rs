
use std::sync::Arc;

use winit::{
    application::ApplicationHandler,
    dpi::{PhysicalPosition, PhysicalSize},
    event::WindowEvent,
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    window::{Window, WindowId},
};

use crate::graphics::{
    camera::CameraState,
    window::WindowState,
};


/// The SceneState holds a Camera, along with any other scene data.
/// It must implement a render() method to render the scene.
pub trait SceneState {
    // Create a new SceneState.
    fn new(wstate: &WindowState) -> Self;

    // Get the current state of the camera.
    fn get_camera_state(&mut self) -> &mut CameraState;

    // Prepare the scene for rendering, e.g. transfer any buffer data to GPU.
    // Called prior to render().
    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue);

    // Called to render the scene.
    fn render(&self, render_pass: &mut wgpu::RenderPass);

    /// Called when the mouse cursor moves within the window.
    /// x and y are in normalized device coordinates (-1 to 1).
    fn mouse_moved(&mut self, x: f32, y: f32);
}


/// WindowedApp implements an event loop for an application that opens a single window.
/// It captures events, and forwards them to the window, camera, or scene.
#[derive(Default)]
pub struct WindowedApp<ST: SceneState> {
    pub window_state: Option<WindowState>,
    pub scene_state: Option<ST>,
}


impl<ST: SceneState> WindowedApp<ST> {
    /// Redraw the scene.
    fn redraw(&mut self) {
        let wstate = self.window_state.as_mut().unwrap();
        let scene_state = self.scene_state.as_mut().unwrap();

        // Do any pre-rendering preparation, such as transferring buffer data to GPU.
        scene_state.prepare_to_render(&mut wstate.queue);

        // Render the scene.
        let render_fn = |render_pass: &mut wgpu::RenderPass| {
            scene_state.render(render_pass);
        };
        wstate.render(render_fn);
        // Emits a new redraw requested event.
        wstate.window.request_redraw();
    }

    /// Resize the window surface.
    fn resized(&mut self, size: PhysicalSize<u32>) {
        let wstate = self.window_state.as_mut().unwrap();
        let render_state = self.scene_state.as_mut().unwrap();

        // Reconfigures the size of the surface. We do not re-render
        // here as this event is always followed up by redraw request.
        wstate.resize(size);

        let camera_state = render_state.get_camera_state();
        camera_state.set_aspect_ratio(size.width, size.height);
        camera_state.update_camera_buffer(&mut wstate.queue);
    }

    /// Handle a mouse moved event.
    fn mouse_moved(&mut self, position: PhysicalPosition<f64>) {
        let wstate = self.window_state.as_mut().unwrap();
        let render_state = self.scene_state.as_mut().unwrap();

        // Convert screen coordinates to normalized device coordinates.
        // Flip the y axis.
        render_state.mouse_moved((position.x / (wstate.size.width as f64) * 2.0 - 1.0) as f32,
                                 (position.y / (wstate.size.height as f64) * -2.0 + 1.0) as f32);
    }
}


impl<ST: SceneState> ApplicationHandler for WindowedApp<ST> {
    /// Platform-agnostic entrypoint that runs on startup.
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        // Open a window.
        let window = Arc::new(
            event_loop
                .create_window(Window::default_attributes())
                .unwrap(),
        );
        // Create the WindowState, which sets everything up.
        let wstate = pollster::block_on(WindowState::new(window.clone()));

        // Create the renderable object, whatever it happens to be.
        let render_state = ST::new(&wstate);

        self.window_state = Some(wstate);
        self.scene_state = Some(render_state);

        window.request_redraw();
    }

    /// Handle a window event.
    fn window_event(&mut self, event_loop: &ActiveEventLoop, _id: WindowId, event: WindowEvent) {
        // Handle events for the window that we opened.
        match event {
            WindowEvent::CloseRequested => {
                println!("The close button was pressed; stopping");
                event_loop.exit();
            }
            WindowEvent::RedrawRequested => {
                self.redraw();
            }
            WindowEvent::Resized(size) => {
                self.resized(size);
            }
            WindowEvent::CursorMoved { position, .. } => {
                self.mouse_moved(position)
            }
            _ => (),
        }
    }
}


pub fn run_application<RT: SceneState>() {
    // wgpu uses `log` for all of our logging, so we initialize a logger with the `env_logger` crate.
    // To change the log level, set the `RUST_LOG` environment variable. See the `env_logger`
    // documentation for more information.
    env_logger::init();

    // Start running an event loop get events from the system.
    let event_loop = EventLoop::new().unwrap();

    // Option 1:
    // When the current loop iteration finishes, immediately begin a new
    // iteration regardless of whether or not new events are available to
    // process. Preferred for applications that want to render as fast as
    // possible, like games.
    event_loop.set_control_flow(ControlFlow::Poll);

    // Option 2:
    // When the current loop iteration finishes, suspend the thread until
    // another event arrives. Helps keeping CPU utilization low if nothing
    // is happening, which is preferred if the application might be idling in
    // the background.
    // event_loop.set_control_flow(ControlFlow::Wait);

    // Create the application, which opens a window, and start passing events to it.
    let mut windowed_app = WindowedApp::<RT> {
        window_state: None,
        scene_state: None,
    };
    event_loop.run_app(&mut windowed_app).unwrap();
}
