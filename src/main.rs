
use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::CpuVertex,
    render::triangle_render_pipeline,
    scene::{Body, Scene},
    window::WindowState,
    window_app::{SceneState, run_application},
};

use nalgebra::Point3;


const TRIANGLE: &[CpuVertex] = &[
    CpuVertex {
        position: Point3::<f32>::new(0.0, 0.1, 0.0),
        color: Point3::new(1.0, 0.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(-0.1, -0.1, 0.0),
        color: Point3::<f32>::new(0.0, 1.0, 0.0)
    },
    CpuVertex {
        position: Point3::<f32>::new(0.1, -0.1, 0.0),
        color: Point3::<f32>::new(0.0, 0.0, 1.0)
    },
];


struct MyTriangleRenderer {
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    scene: Scene,
    needs_update_buffer: bool
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );

        let mut scene = Scene::new(&wstate.device);
        for i in -2..3 {
            for j in -2..3 {
                let p = Point3::<f32>::new(
                    (i as f32)*0.3,
                    (j as f32)*0.3,
                    0.5
                );
                let body = Body::new(p, &TRIANGLE);
                scene.add(body);
            }
        }

        return Self {
            camera_state,
            render_pipeline,
            scene,
            needs_update_buffer: true
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        for b in &mut self.scene.bodies {
            b.rotation += 0.01;
            b.update_view_matrix();
        }
        self.scene.update_buffer(queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.scene.render(render_pass);
    }

    fn mouse_moved(&mut self, x: f32, y: f32) {
        // Convert screen coordinates to world coordinates using stored window dimensions
        let world_point =
            self.camera_state.camera.screen_to_world(x as f32, y as f32).xy();
        println!("mouse_moved {:?}", world_point);
    }
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
