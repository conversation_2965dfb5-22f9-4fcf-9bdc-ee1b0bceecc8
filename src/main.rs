
use rustwin::graphics::{
    camera::CameraState,
    gpu_buffer::Vertex,
    render::{TriangleList, triangle_render_pipeline},
    window::WindowState,
    window_app::{SceneState, run_application},
};

use nalgebra::{
    Point3, Point4, Vector3, Matrix4, Scale3, Similarity3
};


const TRIANGLE: &[CpuVertex] = &[
    CpuVertex { position: Point3(0.0, 0.5, 0.0),   color: Point3(1.0, 0.0, 0.0) },
    CpuVertex { position: Point3(-0.5, -0.5, 0.0), color: Point3(0.0, 1.0, 0.0) },
    CpuVertex { position: Point3(0.5, -0.5, 0.0),  color: Point3(0.0, 0.0, 1.0) },
];


struct MyTriangleRenderer {
    camera_state: CameraState,
    render_pipeline: wgpu::RenderPipeline,
    triangles: TriangleList,
}

impl SceneState for MyTriangleRenderer {
    fn new(wstate: &WindowState) -> Self {
        let mut camera_state = CameraState::new(&wstate.device);
        camera_state.set_aspect_ratio(wstate.size.width, wstate.size.height);

        let render_pipeline = triangle_render_pipeline(
            wstate,
            &camera_state
        );

        // Generate the triangle data.
        let vertices = MY_VERTICES;
        let indices: Vec<u16> = (0..(vertices.len() as u16)).collect();
        let triangles = TriangleList::new(&wstate.device, &vertices, &indices);

        return Self {
            camera_state,
            render_pipeline,
            triangles,
        }
    }

    fn get_camera_state(&mut self) -> &mut CameraState {
        return &mut self.camera_state;
    }

    fn prepare_to_render(&mut self, queue: &mut wgpu::Queue) {
        self.triangles.update_buffer(queue);
    }

    fn render(&self, render_pass: &mut wgpu::RenderPass) {
        render_pass.set_pipeline(&self.render_pipeline);
        render_pass.set_bind_group(0, &self.camera_state.bind_group, &[]);
        self.triangles.render(render_pass);
    }

    fn mouse_moved(&mut self, x: f32, y: f32) {
        // Convert screen coordinates to world coordinates using stored window dimensions
        let world_point =
            self.camera_state.camera.screen_to_world(x as f32, y as f32).xy();
        println!("mouse_moved {:?}", world_point);
    }
}


fn main() {
    run_application::<MyTriangleRenderer>();
}
